# 醫美診所人臉辨識系統產品需求文檔（PRD）

## 1. 產品概述

### 1.1 背景
隨著醫美產業數位化升級，診所對於會員管理、服務體驗與安全性有更高要求。傳統會員管理方式效率低、易出錯，且VIP服務無法即時個性化。人臉辨識技術可提升會員辨識效率，並為VIP會員帶來專屬互動體驗。

### 1.2 產品定位
本系統為醫美診所打造，結合 YOLOv8-face 人臉檢測、Flask 後端服務與自動化會員管理，提供即時人臉辨識、身份驗證、VIP互動及批量會員資料管理。目標用戶為醫美診所前台、管理員及IT維運人員。

### 1.3 主要業務目標
- 提升會員到店辨識效率與準確率
- 降低人工管理成本，提升會員資料安全
- 強化VIP會員體驗，提升客戶滿意度
- 滿足醫美行業對個資保護與合規要求

### 1.4 預期效益
- 會員辨識準確率達98%以上
- 會員資料自動化管理，減少80%人工操作
- VIP會員互動自動化，提升專屬感

## 2. 範圍界定

### 包含範圍
- 人臉辨識模組（YOLOv8-face + Flask）
- 會員資料管理（批量上傳、標註、訓練）
- VIP會員識別與語音互動
- API介面與資料庫設計

### 排除範圍
- 非醫美診所業務流程（如醫療記錄管理）
- 第三方支付、行銷推播等非核心功能

## 3. 用戶流程

### 新會員建檔流程
1. 管理員登入後台，選擇會員資料上傳
2. 批量上傳會員照片資料夾（資料夾名稱=會員姓名）
3. 上傳後點擊自動進行人臉標註與資料建檔
4. 完成後自動觸發模型訓練/更新

### 會員到店辨識流程
1. 會員進入診所，攝影機自動捕捉人臉
2. YOLOv8-face 模型進行人臉檢測與辨識
3. 若為VIP會員，系統自動語音歡迎
4. 前台可即時查詢會員資訊

## 4. 核心功能

### 4.1 人臉辨識模組
- YOLOv8-face 實時人臉檢測與辨識
- Flask API 提供辨識服務
- 支援多攝影機串接
- 身份驗證與辨識紀錄

### 4.2 會員資料管理
- 批量上傳會員照片（資料夾命名=姓名）
- 自動人臉標註、資料建檔
- 自動化模型訓練與更新
- 會員資料查詢、編輯、刪除

### 4.3 VIP會員識別與互動
- VIP會員自動語音歡迎（個性化內容）
- 歡迎語音內容可自訂
- VIP辨識紀錄查詢

### 4.4 權限與安全管理
- 管理員/操作員分級權限
- 操作日誌與異常行為監控
- 敏感資料加密儲存

## 5. 技術架構

- 前端：Web管理後台（React/Vue，未來可擴充）
- 後端：Flask RESTful API
- 人臉辨識：YOLOv8-face + OpenCV
- 資料庫：MySQL（會員/辨識紀錄/權限）
- 語音互動：TTS（可用 pyttsx3 或第三方API）
- 部署：Docker 容器化，支援本地/雲端

## 6. 系統架構圖

```mermaid
graph TD
    A[攝影機] --> B[YOLOv8-face辨識服務(Flask)]
    B --> C[會員資料庫]
    B --> D[Web管理後台]
    B --> E[TTS語音服務]
    D --> C
    D --> B
```

## 7. API設計

### 7.1 會員照片上傳
- POST /api/members/upload
- FormData: { folder.zip }
- 回應: { code, message, data: { 新增會員數, 失敗列表 } }

### 7.2 人臉辨識查詢
- POST /api/face/recognize
- body: { image(base64) }
- 回應: { code, message, data: { member_id, name, is_vip, confidence } }

### 7.3 VIP語音歡迎
- POST /api/vip/greet
- body: { member_id }
- 回應: { code, message }

### 7.4 會員資料查詢/管理
- GET /api/members
- GET /api/members/{id}
- PUT /api/members/{id}
- DELETE /api/members/{id}

## 8. 資料庫設計

### 8.1 會員表（members）
| 欄位         | 型別           | 說明           |
|--------------|----------------|----------------|
| id           | bigint         | 主鍵           |
| name         | varchar(50)    | 會員姓名       |
| is_vip       | tinyint        | 是否VIP        |
| photo_path   | varchar(255)   | 照片路徑       |
| face_vector  | blob           | 人臉特徵向量   |
| created_at   | timestamp      | 建立時間       |
| updated_at   | timestamp      | 更新時間       |

### 8.2 辨識紀錄表（face_logs）
| 欄位         | 型別           | 說明           |
|--------------|----------------|----------------|
| id           | bigint         | 主鍵           |
| member_id    | bigint         | 會員ID         |
| recognized_at| timestamp      | 辨識時間       |
| camera_id    | varchar(50)    | 攝影機編號     |
| confidence   | float          | 相似度         |
| is_vip       | tinyint        | 是否VIP        |

### 8.3 管理員表（admins）
| 欄位         | 型別           | 說明           |
|--------------|----------------|----------------|
| id           | bigint         | 主鍵           |
| username     | varchar(50)    | 帳號           |
| password     | varchar(100)   | 密碼(加密)     |
| role         | varchar(20)    | 角色           |
| created_at   | timestamp      | 建立時間       |


## 9. 非功能需求
- 人臉辨識延遲 < 1 秒
- 系統可用性 > 99.9%
- 支援多攝影機與高併發
- 介面友善，易於維運

## 10. 開發與部署
- 版本控制：Git，語義化版本
- 部署：Docker Compose，支援雲端/本地
- 文件：API文件、操作手冊、維運手冊

## 11. 里程碑
- M1：人臉辨識模組開發完成
- M2：會員管理與自動化訓練上線
- M3：VIP互動與語音功能上線
